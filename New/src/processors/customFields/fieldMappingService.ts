/**
 * Field Mapping Service
 * 
 * Practical service that uses the enhanced mapCCFieldToAPType function
 * to handle field synchronization between CC and AP platforms.
 */

import { mapCCtoAPField } from "./mapping";
import type { GetCCCustomField, APGetCustomFieldType } from "@/type";
import { logInfo, logWarn, logDebug } from "@/utils/logger";
import apiClient from "@/apiClient";

export interface FieldMappingDecision {
	action: "map_to_standard" | "map_to_custom" | "create_custom" | "skip";
	sourceField: GetCCCustomField;
	targetField?: string; // For standard field mappings
	targetCustomField?: APGetCustomFieldType; // For custom field mappings
	confidence: number;
	reason: string;
	notes?: string;
}

/**
 * Analyze a CC field and determine the best mapping strategy for AP
 */
export function analyzeFieldMapping(
	ccField: GetCCCustomField,
	apCustomFields: APGetCustomFieldType[]
): FieldMappingDecision {
	// Use enhanced mapping to get comprehensive analysis with auto-extracted field info
	const enhancedResult = mapCCtoAPField(ccField);

	logDebug("Analyzing field mapping", {
		ccFieldName: ccField.name,
		ccFieldType: ccField.type,
		mappingType: enhancedResult.mappingType,
		confidence: enhancedResult.confidence
	});

	// Case 1: Standard field mapping detected
	if (enhancedResult.mappingType === "standard_field" && enhancedResult.standardFieldMapping) {
		return {
			action: "map_to_standard",
			sourceField: ccField,
			targetField: enhancedResult.standardFieldMapping.targetField,
			confidence: enhancedResult.confidence,
			reason: `CC custom field '${ccField.name}' maps to AP standard field '${enhancedResult.standardFieldMapping.targetField}'`,
			notes: enhancedResult.standardFieldMapping.notes
		};
	}

	// Case 2: Custom field mapping - look for existing AP custom field
	if (enhancedResult.mappingType === "custom_field" && enhancedResult.customFieldType) {
		// Try to find matching AP custom field
		const matchingAPField = findMatchingAPCustomField(ccField, apCustomFields, enhancedResult.customFieldType);
		
		if (matchingAPField) {
			return {
				action: "map_to_custom",
				sourceField: ccField,
				targetCustomField: matchingAPField,
				confidence: enhancedResult.confidence,
				reason: `CC custom field '${ccField.name}' maps to existing AP custom field '${matchingAPField.name}'`
			};
		}

		// No matching AP custom field found - suggest creation
		return {
			action: "create_custom",
			sourceField: ccField,
			confidence: enhancedResult.confidence,
			reason: `No matching AP custom field found for '${ccField.name}'. Suggest creating new ${enhancedResult.customFieldType} field.`
		};
	}

	// Fallback case
	return {
		action: "skip",
		sourceField: ccField,
		confidence: 0,
		reason: "Unable to determine appropriate mapping strategy"
	};
}

/**
 * Find matching AP custom field using enhanced type information
 */
function findMatchingAPCustomField(
	ccField: GetCCCustomField,
	apCustomFields: APGetCustomFieldType[],
	expectedType: string
): APGetCustomFieldType | null {
	return apCustomFields.find(apField => {
		// Type must match
		if (apField.dataType !== expectedType) return false;

		// Name matching (using simple string comparison for now)
		const nameMatch = apField.name.toLowerCase() === ccField.name.toLowerCase() ||
						  apField.name.toLowerCase() === ccField.label.toLowerCase();

		// Field key matching (if available)
		const keyMatch = apField.fieldKey?.split('.').pop()?.toLowerCase() === ccField.name.toLowerCase();

		return nameMatch || keyMatch;
	}) || null;
}

/**
 * Process multiple CC fields and generate mapping decisions
 */
export function generateMappingPlan(
	ccFields: GetCCCustomField[],
	apCustomFields: APGetCustomFieldType[]
): {
	decisions: FieldMappingDecision[];
	summary: {
		standardMappings: number;
		customMappings: number;
		newFieldsNeeded: number;
		skipped: number;
	};
} {
	const decisions = ccFields.map(ccField => analyzeFieldMapping(ccField, apCustomFields));

	const summary = {
		standardMappings: decisions.filter(d => d.action === "map_to_standard").length,
		customMappings: decisions.filter(d => d.action === "map_to_custom").length,
		newFieldsNeeded: decisions.filter(d => d.action === "create_custom").length,
		skipped: decisions.filter(d => d.action === "skip").length
	};

	logInfo("Field mapping plan generated", {
		totalFields: ccFields.length,
		...summary
	});

	return { decisions, summary };
}

/**
 * Execute field mapping decisions - actually creates fields and stores mappings
 */
export async function executeMappingPlan(decisions: FieldMappingDecision[]): Promise<{
	executed: number;
	failed: number;
	skipped: number;
	results: Array<{
		decision: FieldMappingDecision;
		success: boolean;
		error?: string;
		createdField?: APGetCustomFieldType;
	}>;
}> {
	const results = [];
	let executed = 0;
	let failed = 0;
	let skipped = 0;

	for (const decision of decisions) {
		try {
			switch (decision.action) {
				case "map_to_standard":
					logInfo(`✅ Standard field mapping: ${decision.sourceField.name} → ${decision.targetField}`);
					// Store the standard field mapping in database for future value sync
					await storeFieldMapping(decision, "standard_field");
					results.push({ decision, success: true });
					executed++;
					break;

				case "map_to_custom":
					logInfo(`🔗 Custom field mapping: ${decision.sourceField.name} → ${decision.targetCustomField?.name}`);
					// Store the custom field mapping in database for future value sync
					await storeFieldMapping(decision, "custom_field");
					results.push({ decision, success: true });
					executed++;
					break;

				case "create_custom":
					logInfo(`🆕 Creating AP custom field for: ${decision.sourceField.name}`);
					// Actually create the AP custom field
					const createdField = await createAPCustomField(decision.sourceField);
					// Store the new field mapping
					await storeFieldMapping({
						...decision,
						targetCustomField: createdField
					}, "custom_field");
					results.push({ decision, success: true, createdField });
					executed++;
					break;

				case "skip":
					logWarn(`⏭️ Skipping field: ${decision.sourceField.name} - ${decision.reason}`);
					results.push({ decision, success: true });
					skipped++;
					break;
			}
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : String(error);
			logWarn(`❌ Failed to execute mapping for ${decision.sourceField.name}: ${errorMessage}`);
			results.push({ decision, success: false, error: errorMessage });
			failed++;
		}
	}

	logInfo("Field mapping execution completed", {
		total: decisions.length,
		executed,
		failed,
		skipped
	});

	return { executed, failed, skipped, results };
}

/**
 * Create an AP custom field based on CC field definition
 */
async function createAPCustomField(ccField: GetCCCustomField): Promise<APGetCustomFieldType> {
	// Map CC field to AP field type
	const mappingResult = mapCCtoAPField(ccField);

	if (mappingResult.mappingType !== "custom_field" || !mappingResult.customFieldType) {
		throw new Error(`Cannot create AP custom field for ${ccField.name}: not a custom field mapping`);
	}

	// Create the AP custom field data
	const apFieldData = {
		name: ccField.label || ccField.name, // Use label as primary name, fallback to name
		dataType: mappingResult.customFieldType,
		placeholder: `Synced from CC field: ${ccField.name}`,
		// Add options for select fields
		...(ccField.allowedValues?.length > 0 && {
			picklistOptions: ccField.allowedValues.map(av => av.value)
		})
	};

	logDebug("Creating AP custom field", {
		ccFieldName: ccField.name,
		ccFieldType: ccField.type,
		apFieldData
	});

	// Create the field using the API client
	const createdField = await apiClient.ap.apCustomfield.create(apFieldData);

	logInfo(`✅ Created AP custom field: ${createdField.name} (${createdField.dataType})`, {
		apFieldId: createdField.id,
		ccFieldId: ccField.id,
		ccFieldName: ccField.name
	});

	return createdField;
}

/**
 * Store field mapping in database for future value synchronization
 */
async function storeFieldMapping(
	decision: FieldMappingDecision,
	mappingType: "standard_field" | "custom_field"
): Promise<void> {
	// This would store the mapping in the database
	// For now, just log the mapping that should be stored
	logInfo("📝 Field mapping to store in database", {
		ccFieldId: decision.sourceField.id,
		ccFieldName: decision.sourceField.name,
		mappingType,
		targetField: decision.targetField,
		targetCustomFieldId: decision.targetCustomField?.id,
		targetCustomFieldName: decision.targetCustomField?.name,
		confidence: decision.confidence,
		notes: decision.notes
	});

	// TODO: Implement actual database storage
	// await db.insert(customFields).values({
	//   ccId: decision.sourceField.id,
	//   apId: decision.targetCustomField?.id,
	//   name: decision.sourceField.name,
	//   label: decision.sourceField.label,
	//   type: decision.sourceField.type,
	//   ccConfig: decision.sourceField,
	//   apConfig: decision.targetCustomField,
	//   mappingType: mappingType
	// });
}
