/**
 * Custom Fields Handler
 *
 * Simple HTTP handler for custom field requests.
 */

import type { Context } from "hono";
import { synchronizeCustomFields } from "@/processors/customFields";

/**
 * Handle Custom Fields webhook events
 *
 * Simple handler that returns an empty object response.
 *
 * @param c - Hono context object containing request data and utilities
 * @returns Promise resolving to HTTP Response with empty object
 */
export async function cfHandler(c: Context): Promise<Response> {
	try {
		const result = await synchronizeCustomFields();
		return c.json(result);
	} catch (error) {
		return c.json({ error: "Internal server error" }, 500);
	}
}
