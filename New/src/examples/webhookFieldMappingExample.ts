/**
 * Example: Using Enhanced Field Mapping in Webhook Processing
 * 
 * This example shows how to use the enhanced mapCCFieldToAPType function
 * in real webhook processing scenarios.
 */

import { mapCCtoAPField } from "../processors/customFields/mapping";
import type { GetCCCustomField } from "@/type";

/**
 * Example 1: Processing CC webhook with custom fields
 */
export async function processCCWebhookWithEnhancedMapping(ccPatientData: any) {
	console.log("🔄 Processing CC webhook with enhanced field mapping...");

	if (!ccPatientData.customFields || !Array.isArray(ccPatientData.customFields)) {
		return;
	}

	// Get CC custom field definitions (you'd fetch these from your API)
	const ccCustomFields: GetCCCustomField[] = []; // await apiClient.cc.ccCustomfieldReq.all();

	for (const customFieldValue of ccPatientData.customFields) {
		const fieldId = customFieldValue.field?.id;
		if (!fieldId) continue;

		// Find the field definition
		const ccField = ccCustomFields.find(f => f.id.toString() === fieldId);
		if (!ccField) continue;

		// Use enhanced mapping to determine how to handle this field
		const mappingResult = mapCCtoAPField(ccField);

		console.log(`📋 Field: ${ccField.name}`, {
			mappingType: mappingResult.mappingType,
			confidence: mappingResult.confidence
		});

		if (mappingResult.mappingType === "standard_field") {
			// Handle standard field mapping
			await handleStandardFieldMapping(
				customFieldValue,
				mappingResult.standardFieldMapping!.targetField,
				ccPatientData
			);
		} else {
			// Handle custom field mapping
			await handleCustomFieldMapping(
				customFieldValue,
				ccField,
				mappingResult.customFieldType!
			);
		}
	}
}

/**
 * Handle mapping to AP standard fields
 */
async function handleStandardFieldMapping(
	customFieldValue: any,
	apStandardField: string,
	patientData: any
) {
	console.log(`🎯 Mapping to AP standard field: ${apStandardField}`);

	// Extract the value from CC custom field format
	const value = extractCustomFieldValue(customFieldValue);
	
	if (value) {
		// Update the patient data with standard field value
		patientData[apStandardField] = value;
		console.log(`✅ Set ${apStandardField} = ${value}`);
	}
}

/**
 * Handle mapping to AP custom fields
 */
async function handleCustomFieldMapping(
	customFieldValue: any,
	ccField: GetCCCustomField,
	apFieldType: string
) {
	console.log(`🔗 Mapping to AP custom field type: ${apFieldType}`);

	// Extract and convert the value based on field type
	const convertedValue = convertCustomFieldValue(customFieldValue, ccField, apFieldType);
	
	if (convertedValue !== null) {
		// Add to custom fields array for AP
		// This would be added to your AP contact update payload
		console.log(`✅ Custom field ${ccField.name} converted for AP:`, convertedValue);
	}
}

/**
 * Example 2: Reverse direction - AP to CC mapping
 */
export async function processAPWebhookWithEnhancedMapping(apContactData: any) {
	console.log("🔄 Processing AP webhook with enhanced field mapping...");

	// Example: Check if AP standard fields should map to CC custom fields
	const apStandardFields = ['email', 'phone', 'firstName', 'lastName'];

	for (const fieldName of apStandardFields) {
		const fieldValue = apContactData[fieldName];
		if (!fieldValue) continue;

		// Check if this AP standard field should map to a CC custom field
		const mappingResult = mapCCFieldToAPType(
			{ type: "text", allowMultipleValues: false }, // Dummy field for type checking
			{
				fieldName: fieldName,
				sourcePlatform: "ap",
				targetPlatform: "cc"
			}
		) as EnhancedFieldMappingResult;

		if (mappingResult.mappingType === "standard_field") {
			console.log(`🎯 AP standard field '${fieldName}' should map to CC custom field:`, 
				mappingResult.standardFieldMapping?.targetField);
			
			// Handle the mapping to CC custom field
			await handleAPStandardToCCCustomMapping(fieldName, fieldValue, mappingResult);
		}
	}
}

/**
 * Handle AP standard field to CC custom field mapping
 */
async function handleAPStandardToCCCustomMapping(
	apFieldName: string,
	fieldValue: any,
	mappingResult: EnhancedFieldMappingResult
) {
	console.log(`🔄 Mapping AP standard field '${apFieldName}' to CC custom field`);

	// You would implement the actual CC custom field update here
	// This might involve finding the CC custom field ID and formatting the value correctly
}

/**
 * Utility: Extract value from CC custom field format
 */
function extractCustomFieldValue(customFieldValue: any): string | null {
	if (customFieldValue.values && Array.isArray(customFieldValue.values)) {
		// Handle different value formats
		const firstValue = customFieldValue.values[0];
		if (firstValue) {
			return firstValue.value || firstValue.id?.toString() || null;
		}
	}
	return null;
}

/**
 * Utility: Convert custom field value based on target type
 */
function convertCustomFieldValue(
	customFieldValue: any,
	ccField: GetCCCustomField,
	apFieldType: string
): any {
	const rawValue = extractCustomFieldValue(customFieldValue);
	if (!rawValue) return null;

	// Convert based on AP field type
	switch (apFieldType) {
		case "TEXT":
		case "LARGE_TEXT":
			return rawValue;
		
		case "NUMERICAL":
			const num = parseFloat(rawValue);
			return isNaN(num) ? null : num;
		
		case "EMAIL":
			// Validate email format
			return rawValue.includes('@') ? rawValue : null;
		
		case "PHONE":
			// Clean phone number
			return rawValue.replace(/[^\d+\-\s()]/g, '');
		
		case "TEXTBOX_LIST":
			// Handle multi-value fields
			if (ccField.allowMultipleValues && customFieldValue.values) {
				return customFieldValue.values.map((v: any) => v.value || v.id?.toString()).filter(Boolean);
			}
			return [rawValue];
		
		default:
			return rawValue;
	}
}

/**
 * Example 3: Batch field analysis
 */
export async function analyzeAllFieldMappings() {
	console.log("🔍 Analyzing all field mappings...");

	// This would fetch your actual field data
	const ccFields: GetCCCustomField[] = []; // await apiClient.cc.ccCustomfieldReq.all();
	
	const analysisResults = ccFields.map(ccField => {
		const result = mapCCtoAPField(ccField);

		return {
			ccFieldName: ccField.name,
			ccFieldType: ccField.type,
			mappingType: result.mappingType,
			confidence: result.confidence,
			recommendation: result.mappingType === "standard_field" 
				? `Map to AP standard field: ${result.standardFieldMapping?.targetField}`
				: `Create AP custom field of type: ${result.customFieldType}`
		};
	});

	console.table(analysisResults);
	return analysisResults;
}
