/**
 * Example: Complete Field Mapping Service Usage
 * 
 * This example shows how to use the field mapping service to analyze CC fields,
 * generate a mapping plan, and execute it to create AP custom fields.
 */

import { 
	analyzeFieldMapping, 
	generateMappingPlan, 
	executeMappingPlan 
} from "../processors/customFields/fieldMappingService";
import type { GetCCCustomField, APGetCustomFieldType } from "@/type";

/**
 * Example: Complete field mapping workflow
 */
export async function demonstrateFieldMappingWorkflow() {
	console.log("🚀 Demonstrating complete field mapping workflow...");

	// Sample CC fields (would come from API)
	const ccFields: GetCCCustomField[] = [
		{
			id: 1,
			name: "phone",
			label: "Phone Number",
			type: "telephone",
			allowMultipleValues: false,
			validation: "",
			color: null,
			positions: [],
			useCustomSort: null,
			isRequired: false,
			allowedValues: [],
			defaultValues: []
		},
		{
			id: 2,
			name: "customNote",
			label: "Patient Notes",
			type: "textarea",
			allowMultipleValues: false,
			validation: "",
			color: null,
			positions: [],
			useCustomSort: null,
			isRequired: false,
			allowedValues: [],
			defaultValues: []
		},
		{
			id: 3,
			name: "allergies",
			label: "Known Allergies",
			type: "text",
			allowMultipleValues: true,
			validation: "",
			color: null,
			positions: [],
			useCustomSort: null,
			isRequired: false,
			allowedValues: [],
			defaultValues: []
		}
	];

	// Sample existing AP custom fields (would come from API)
	const apCustomFields: APGetCustomFieldType[] = [
		{
			id: "ap_field_1",
			name: "Patient Notes",
			dataType: "LARGE_TEXT",
			fieldKey: "contact.patient_notes"
		}
	];

	console.log("\n📋 Step 1: Analyze individual field mappings");
	for (const ccField of ccFields) {
		const decision = analyzeFieldMapping(ccField, apCustomFields);
		console.log(`Field: ${ccField.name} → Action: ${decision.action} (confidence: ${decision.confidence})`);
		console.log(`  Reason: ${decision.reason}`);
	}

	console.log("\n📊 Step 2: Generate complete mapping plan");
	const plan = generateMappingPlan(ccFields, apCustomFields);
	
	console.log("Mapping Plan Summary:");
	console.log(`  📍 Standard mappings: ${plan.summary.standardMappings}`);
	console.log(`  🔗 Custom mappings: ${plan.summary.customMappings}`);
	console.log(`  🆕 New fields needed: ${plan.summary.newFieldsNeeded}`);
	console.log(`  ⏭️ Skipped: ${plan.summary.skipped}`);

	console.log("\n📝 Step 3: Execute mapping plan");
	try {
		const results = await executeMappingPlan(plan.decisions);
		
		console.log("Execution Results:");
		console.log(`  ✅ Executed: ${results.executed}`);
		console.log(`  ❌ Failed: ${results.failed}`);
		console.log(`  ⏭️ Skipped: ${results.skipped}`);

		// Show details of created fields
		const createdFields = results.results.filter(r => r.createdField);
		if (createdFields.length > 0) {
			console.log("\n🆕 Created AP Custom Fields:");
			createdFields.forEach(result => {
				console.log(`  - ${result.createdField?.name} (${result.createdField?.dataType})`);
				console.log(`    ID: ${result.createdField?.id}`);
			});
		}

		// Show any failures
		const failures = results.results.filter(r => !r.success);
		if (failures.length > 0) {
			console.log("\n❌ Failed Mappings:");
			failures.forEach(result => {
				console.log(`  - ${result.decision.sourceField.name}: ${result.error}`);
			});
		}

	} catch (error) {
		console.error("❌ Failed to execute mapping plan:", error);
	}
}

/**
 * Example: Analyze a single field
 */
export function demonstrateSingleFieldAnalysis() {
	console.log("\n🔍 Demonstrating single field analysis...");

	const ccField: GetCCCustomField = {
		id: 1,
		name: "email",
		label: "Email Address",
		type: "email",
		allowMultipleValues: false,
		validation: "",
		color: null,
		positions: [],
		useCustomSort: null,
		isRequired: false,
		allowedValues: [],
		defaultValues: []
	};

	const apCustomFields: APGetCustomFieldType[] = [];

	const decision = analyzeFieldMapping(ccField, apCustomFields);
	
	console.log("Analysis Result:");
	console.log(`  Field: ${ccField.name} (${ccField.type})`);
	console.log(`  Action: ${decision.action}`);
	console.log(`  Confidence: ${decision.confidence}`);
	console.log(`  Reason: ${decision.reason}`);
	
	if (decision.targetField) {
		console.log(`  Target Standard Field: ${decision.targetField}`);
	}
	
	if (decision.targetCustomField) {
		console.log(`  Target Custom Field: ${decision.targetCustomField.name}`);
	}
}

/**
 * Example: What the service does
 */
export function explainFieldMappingService() {
	console.log(`
🎯 Field Mapping Service Purpose:

The executeMappingPlan function is the ACTION part of field mapping:

1. 📋 ANALYSIS (generateMappingPlan):
   - Analyzes CC fields vs existing AP fields
   - Determines what action to take for each field
   - Creates a plan with decisions

2. 🚀 EXECUTION (executeMappingPlan):
   - Actually creates missing AP custom fields
   - Stores field mappings in database
   - Sets up future value synchronization

3. 🔄 VALUE SYNC (future):
   - Uses stored mappings to sync patient data
   - Converts values between field types
   - Handles TEXTBOX_LIST and other complex types

WHY IT HAD TODOs:
- The analysis part was complete
- The execution part needed actual API calls
- Database storage needed implementation
- Now it's COMPLETE and functional!
	`);
}

// Run examples
if (require.main === module) {
	explainFieldMappingService();
	demonstrateSingleFieldAnalysis();
	// demonstrateFieldMappingWorkflow(); // Uncomment to test with real API
}
