/**
 * Example: Simple Field Mapping Functions
 *
 * This example demonstrates the dead simple API - no fucking extra parameters needed!
 */

import { mapCCtoAPField, mapAPtoCCField } from "../processors/customFields/mapping";
import type { GetCCCustomField } from "@/type";

/**
 * Example showing the SIMPLE API usage patterns
 */
export function demonstrateSimpleAPI() {
	console.log("🚀 Demonstrating SIMPLE field mapping functions...");

	// Sample CC field data
	const ccField: GetCCCustomField = {
		id: 1,
		name: "phone",
		label: "Phone Number",
		type: "telephone",
		allowMultipleValues: false,
		validation: "",
		color: null,
		positions: [],
		useCustomSort: null,
		isRequired: false,
		allowedValues: [],
		defaultValues: []
	};

	// ✅ SIMPLE: CC to AP mapping - NO EXTRA PARAMETERS!
	console.log("\n📱 CC to AP mapping (SIMPLE):");
	const result1 = mapCCtoAPField(ccField);
	console.log({
		mappingType: result1.mappingType,
		confidence: result1.confidence,
		fieldName: result1.fieldName,
		fieldLabel: result1.fieldLabel
	});

	// ✅ SIMPLE: AP to CC mapping - NO EXTRA PARAMETERS!
	console.log("\n📱 AP to CC mapping (SIMPLE):");
	const apField = {
		name: "email",
		label: "Email Address",
		type: "email",
		allowMultipleValues: false
	};
	const result2 = mapAPtoCCField(apField);
	console.log({
		mappingType: result2.mappingType,
		confidence: result2.confidence,
		fieldName: result2.fieldName,
		fieldLabel: result2.fieldLabel
	});

	// ✅ Custom field example
	const customField: GetCCCustomField = {
		id: 2,
		name: "customNote",
		label: "Custom Patient Note",
		type: "textarea",
		allowMultipleValues: false,
		validation: "",
		color: null,
		positions: [],
		useCustomSort: null,
		isRequired: false,
		allowedValues: [],
		defaultValues: []
	};

	console.log("\n📝 Custom field mapping:");
	const result3 = mapCCtoAPField(customField);
	console.log({
		mappingType: result3.mappingType,
		customFieldType: result3.customFieldType,
		confidence: result3.confidence
	});
}

/**
 * Comparison: Old vs New API
 */
export function compareOldVsNewAPI() {
	console.log("\n🔄 API Comparison: Old vs New");

	const ccField: GetCCCustomField = {
		id: 1,
		name: "email",
		label: "Email Address",
		type: "email",
		allowMultipleValues: false,
		validation: "",
		color: null,
		positions: [],
		useCustomSort: null,
		isRequired: false,
		allowedValues: [],
		defaultValues: []
	};

	// ❌ OLD WAY (fucking complicated)
	console.log("\n❌ Old way (fucking complicated):");
	console.log(`mapCCFieldToAPType(ccField, {
		fieldName: ccField.name,        // ← Redundant!
		fieldLabel: ccField.label,      // ← Redundant!
		sourcePlatform: "cc",           // ← Redundant!
		targetPlatform: "ap"            // ← Redundant!
	})`);

	// ✅ NEW WAY (dead simple)
	console.log("\n✅ New way (dead simple):");
	console.log(`mapCCtoAPField(ccField)`);

	const result = mapCCtoAPField(ccField);
	console.log("Result:", {
		mappingType: result.mappingType,
		targetField: result.standardFieldMapping?.targetField,
		confidence: result.confidence
	});
}

/**
 * Both directions are simple now
 */
export function demonstrateBothDirections() {
	console.log("\n🔄 Both directions are simple:");

	// CC to AP
	const ccField: GetCCCustomField = {
		id: 1,
		name: "mobile",
		label: "Mobile Phone",
		type: "telephone",
		allowMultipleValues: false,
		validation: "",
		color: null,
		positions: [],
		useCustomSort: null,
		isRequired: false,
		allowedValues: [],
		defaultValues: []
	};

	console.log("\n📱 CC → AP:");
	console.log(`mapCCtoAPField(ccField)`);
	const result1 = mapCCtoAPField(ccField);
	console.log(`Result: ${result1.mappingType}`);

	// AP to CC
	const apField = {
		name: "email",
		label: "Email Address",
		type: "email",
		allowMultipleValues: false
	};

	console.log("\n📧 AP → CC:");
	console.log(`mapAPtoCCField(apField)`);
	const result2 = mapAPtoCCField(apField);
	console.log(`Result: ${result2.mappingType}`);
}

// Run the examples
if (require.main === module) {
	demonstrateSimpleAPI();
	compareOldVsNewAPI();
	demonstrateBothDirections();
}
